'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslation } from 'react-i18next'
import { CheckCircle, AlertCircle, User, Phone, MapPin, Heart, FileText, Edit } from 'lucide-react'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert'

import { reviewSubmitSchema, ReviewSubmitFormData } from '@/lib/validation/beneficiary-registration'
import { useMultiStepForm } from '@/components/forms/multi-step-form'
import { zakatCategoryLabels } from '@/lib/mock-data'
import { cn } from '@/lib/utils'

interface ReviewSubmitStepProps {
  onValidationChange?: (isValid: boolean) => void
}

export const ReviewSubmitStep: React.FC<ReviewSubmitStepProps> = ({
  onValidationChange,
}) => {
  const { t, i18n } = useTranslation()
  const { formData, updateFormData, goToStep } = useMultiStepForm()

  const form = useForm({
    resolver: zodResolver(reviewSubmitSchema),
    defaultValues: {
      termsAccepted: formData.reviewSubmit?.termsAccepted || false,
      dataAccuracyConfirmed: formData.reviewSubmit?.dataAccuracyConfirmed || false,
      privacyPolicyAccepted: formData.reviewSubmit?.privacyPolicyAccepted || false,
    },
    mode: 'onChange',
  })

  const { watch, formState: { isValid, errors } } = form

  // Watch for form changes and update parent form data
  React.useEffect(() => {
    const subscription = watch((value) => {
      updateFormData({
        reviewSubmit: value as ReviewSubmitFormData,
      })
      onValidationChange?.(isValid)
    })
    return () => subscription.unsubscribe()
  }, [watch, updateFormData, onValidationChange, isValid])

  // Notify parent of initial validation state
  React.useEffect(() => {
    onValidationChange?.(isValid)
  }, [isValid, onValidationChange])

  const isRTL = i18n.language === 'ar'

  // Helper function to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const personalDetails = formData.personalDetails
  const contactInfo = formData.contactInformation
  const eligibility = formData.eligibilityCriteria
  const documentation = formData.documentation

  return (
    <div className="space-y-6">
      {/* Summary Alert */}
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertTitle>{t('review_information')}</AlertTitle>
        <AlertDescription>
          {t('review_information_description')}
        </AlertDescription>
      </Alert>

      {/* Personal Details Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {t('personal_information')}
            </CardTitle>
            <Button variant="outline" size="sm" onClick={() => goToStep(0)}>
              <Edit className="h-4 w-4 mr-2" />
              {t('edit')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('name_arabic')}</p>
              <p className="text-right" dir="rtl">{personalDetails?.fullNameAr}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('name_english')}</p>
              <p dir="ltr">{personalDetails?.fullNameEn}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('national_id')}</p>
              <p className="font-mono" dir="ltr">{personalDetails?.nationalId}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('date_of_birth')}</p>
              <p>
                {personalDetails?.dateOfBirth && format(personalDetails.dateOfBirth, 'PPP', {
                  locale: isRTL ? ar : undefined,
                })}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('gender')}</p>
              <p>{personalDetails?.gender && t(personalDetails.gender)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('marital_status')}</p>
              <p>{personalDetails?.maritalStatus && t(personalDetails.maritalStatus)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              {t('contact_info')}
            </CardTitle>
            <Button variant="outline" size="sm" onClick={() => goToStep(1)}>
              <Edit className="h-4 w-4 mr-2" />
              {t('edit')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('phone_number')}</p>
              <p className="font-mono" dir="ltr">{contactInfo?.phoneNumber}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('email')}</p>
              <p dir="ltr">{contactInfo?.email || t('not_provided')}</p>
            </div>
            <div className="md:col-span-2">
              <p className="text-sm font-medium text-muted-foreground">{t('address')}</p>
              <p className={cn(isRTL && 'text-right')} dir={isRTL ? 'rtl' : 'ltr'}>
                {contactInfo?.address}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('city')}</p>
              <p className={cn(isRTL && 'text-right')} dir={isRTL ? 'rtl' : 'ltr'}>
                {contactInfo?.city}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('region')}</p>
              <p className={cn(isRTL && 'text-right')} dir={isRTL ? 'rtl' : 'ltr'}>
                {contactInfo?.region}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Eligibility Criteria Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5" />
              {t('eligibility_criteria')}
            </CardTitle>
            <Button variant="outline" size="sm" onClick={() => goToStep(2)}>
              <Edit className="h-4 w-4 mr-2" />
              {t('edit')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('primary_category')}</p>
              <Badge variant="outline" className="mt-1">
                {eligibility?.primaryCategory &&
                  (isRTL
                    ? zakatCategoryLabels[eligibility.primaryCategory as keyof typeof zakatCategoryLabels]?.ar
                    : zakatCategoryLabels[eligibility.primaryCategory as keyof typeof zakatCategoryLabels]?.en
                  )
                }
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{t('applicable_categories')}</p>
              <div className="flex flex-wrap gap-2 mt-1">
                {eligibility?.zakatCategories?.map((category: any) => (
                  <Badge key={category} variant="secondary">
                    {isRTL ? zakatCategoryLabels[category as keyof typeof zakatCategoryLabels]?.ar : zakatCategoryLabels[category as keyof typeof zakatCategoryLabels]?.en}
                  </Badge>
                ))}
              </div>
            </div>
            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('family_size')}</p>
                <p>{eligibility?.familySize}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('dependents')}</p>
                <p>{eligibility?.dependents}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('monthly_income')}</p>
                <p>{eligibility?.monthlyIncome ? formatCurrency(eligibility.monthlyIncome) : t('not_provided')}</p>
              </div>
            </div>
            {eligibility?.hasSpecialNeeds && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('special_needs_details')}</p>
                <p className={cn('text-sm', isRTL && 'text-right')} dir={isRTL ? 'rtl' : 'ltr'}>
                  {eligibility.specialNeedsDescription}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Documentation Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t('uploaded_documents')}
            </CardTitle>
            <Button variant="outline" size="sm" onClick={() => goToStep(3)}>
              <Edit className="h-4 w-4 mr-2" />
              {t('edit')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {documentation?.documents?.map((doc: any, index: number) => (
              <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                <FileText className="h-5 w-5 text-muted-foreground" />
                <div className="flex-1">
                  <p className="font-medium">{doc.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {t(doc.type)} • {Math.round(doc.size / 1024)} KB
                  </p>
                </div>
                <Badge variant={doc.type === 'national_id' ? 'destructive' : 'secondary'}>
                  {doc.type === 'national_id' ? t('required') : t('optional')}
                </Badge>
              </div>
            ))}
            {(!documentation?.documents || documentation.documents.length === 0) && (
              <p className="text-muted-foreground text-center py-4">
                {t('no_documents_uploaded')}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Terms and Conditions */}
      <Card>
        <CardHeader>
          <CardTitle>{t('terms_and_conditions')}</CardTitle>
          <CardDescription>
            {t('terms_conditions_description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="termsAccepted"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        {t('accept_terms_conditions')}
                      </FormLabel>
                      <FormDescription>
                        {t('terms_conditions_text')}
                      </FormDescription>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dataAccuracyConfirmed"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        {t('confirm_data_accuracy')}
                      </FormLabel>
                      <FormDescription>
                        {t('data_accuracy_text')}
                      </FormDescription>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="privacyPolicyAccepted"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        {t('accept_privacy_policy')}
                      </FormLabel>
                      <FormDescription>
                        {t('privacy_policy_text')}
                      </FormDescription>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
