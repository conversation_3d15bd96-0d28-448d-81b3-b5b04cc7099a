'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslation } from 'react-i18next'
import { Users, DollarSign, Heart, Info } from 'lucide-react'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert'

import { eligibilityCriteriaSchema, EligibilityCriteriaFormData } from '@/lib/validation/beneficiary-registration'
import { useMultiStepForm } from '@/components/forms/multi-step-form'
import { zakatCategoryLabels } from '@/lib/mock-data'
import { ZakatCategory } from '@/lib/types'
import { cn } from '@/lib/utils'

interface EligibilityCriteriaStepProps {
  onValidationChange?: (isValid: boolean) => void
}

export const EligibilityCriteriaStep: React.FC<EligibilityCriteriaStepProps> = ({
  onValidationChange,
}) => {
  const { t, i18n } = useTranslation()
  const { formData, updateFormData } = useMultiStepForm()

  const form = useForm({
    resolver: zodResolver(eligibilityCriteriaSchema),
    defaultValues: {
      primaryCategory: formData.eligibilityCriteria?.primaryCategory || 'fuqara',
      zakatCategories: formData.eligibilityCriteria?.zakatCategories || ['fuqara'],
      familySize: formData.eligibilityCriteria?.familySize || 1,
      dependents: formData.eligibilityCriteria?.dependents || 0,
      monthlyIncome: formData.eligibilityCriteria?.monthlyIncome || 0,
      hasSpecialNeeds: formData.eligibilityCriteria?.hasSpecialNeeds || false,
      specialNeedsDescription: formData.eligibilityCriteria?.specialNeedsDescription || '',
    },
    mode: 'onChange',
  })

  const { watch, formState: { isValid, errors } } = form
  const watchedValues = watch()

  // Watch for form changes and update parent form data
  React.useEffect(() => {
    const subscription = watch((value) => {
      updateFormData({
        eligibilityCriteria: value as EligibilityCriteriaFormData,
      })
      onValidationChange?.(isValid)
    })
    return () => subscription.unsubscribe()
  }, [watch, updateFormData, onValidationChange, isValid])

  // Notify parent of initial validation state
  React.useEffect(() => {
    onValidationChange?.(isValid)
  }, [isValid, onValidationChange])

  const isRTL = i18n.language === 'ar'

  // Handle category selection
  const handleCategoryChange = (category: ZakatCategory, checked: boolean) => {
    const currentCategories = form.getValues('zakatCategories')
    let newCategories: ZakatCategory[]

    if (checked) {
      newCategories = [...currentCategories, category]
    } else {
      newCategories = currentCategories.filter(c => c !== category)
    }

    form.setValue('zakatCategories', newCategories)

    // If primary category is unchecked, set a new primary category
    const primaryCategory = form.getValues('primaryCategory')
    if (!checked && primaryCategory === category && newCategories.length > 0) {
      form.setValue('primaryCategory', newCategories[0])
    }
  }

  // Handle primary category change
  const handlePrimaryCategoryChange = (category: ZakatCategory) => {
    const currentCategories = form.getValues('zakatCategories')
    
    // Ensure primary category is included in selected categories
    if (!currentCategories.includes(category)) {
      form.setValue('zakatCategories', [...currentCategories, category])
    }
    
    form.setValue('primaryCategory', category)
  }

  const zakatCategories = Object.entries(zakatCategoryLabels) as [ZakatCategory, { ar: string; en: string }][]

  return (
    <div className="space-y-6">
      {/* Islamic Compliance Notice */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>{t('islamic_compliance_notice')}</AlertTitle>
        <AlertDescription>
          {t('zakat_categories_description')}
        </AlertDescription>
      </Alert>

      {/* Zakat Categories Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5" />
            {t('zakat_categories')}
          </CardTitle>
          <CardDescription>
            {t('select_applicable_categories')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <div className="space-y-6">
              {/* Category Selection */}
              <FormField
                control={form.control}
                name="zakatCategories"
                render={() => (
                  <FormItem>
                    <FormLabel>{t('applicable_categories')}</FormLabel>
                    <div className="grid gap-3 md:grid-cols-2">
                      {zakatCategories.map(([category, labels]) => (
                        <FormField
                          key={category}
                          control={form.control}
                          name="zakatCategories"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={category}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(category)}
                                    onCheckedChange={(checked) => 
                                      handleCategoryChange(category, checked as boolean)
                                    }
                                  />
                                </FormControl>
                                <div className="space-y-1 leading-none">
                                  <FormLabel className="text-sm font-medium">
                                    {isRTL ? labels?.ar : labels?.en}
                                  </FormLabel>
                                  <FormDescription className="text-xs">
                                    {t(`${category}_description`)}
                                  </FormDescription>
                                </div>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Primary Category */}
              <FormField
                control={form.control}
                name="primaryCategory"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('primary_category')}</FormLabel>
                    <Select 
                      onValueChange={handlePrimaryCategoryChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className={cn(
                          errors.primaryCategory && 'border-destructive'
                        )}>
                          <SelectValue placeholder={t('select_primary_category')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {watchedValues.zakatCategories?.map((category: any) => (
                          <SelectItem key={category} value={category}>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">
                                {isRTL ? zakatCategoryLabels[category as keyof typeof zakatCategoryLabels]?.ar : zakatCategoryLabels[category as keyof typeof zakatCategoryLabels]?.en}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {t('primary_category_description')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </Form>
        </CardContent>
      </Card>

      {/* Family and Financial Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('family_financial_info')}
          </CardTitle>
          <CardDescription>
            {t('family_financial_description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <div className="grid gap-6">
              {/* Family Size and Dependents */}
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="familySize"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        {t('family_size')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          max="20"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                          className={cn(
                            errors.familySize && 'border-destructive'
                          )}
                        />
                      </FormControl>
                      <FormDescription>
                        {t('family_size_description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dependents"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        {t('dependents')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="19"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          className={cn(
                            errors.dependents && 'border-destructive'
                          )}
                        />
                      </FormControl>
                      <FormDescription>
                        {t('dependents_description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Monthly Income */}
              <FormField
                control={form.control}
                name="monthlyIncome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      {t('monthly_income')} ({t('optional')})
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        max="50000"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        className={cn(
                          'font-mono',
                          errors.monthlyIncome && 'border-destructive'
                        )}
                      />
                    </FormControl>
                    <FormDescription>
                      {t('monthly_income_description')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Special Needs */}
              <FormField
                control={form.control}
                name="hasSpecialNeeds"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        {t('has_special_needs')}
                      </FormLabel>
                      <FormDescription>
                        {t('special_needs_description')}
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {/* Special Needs Description */}
              {watchedValues.hasSpecialNeeds && (
                <FormField
                  control={form.control}
                  name="specialNeedsDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('special_needs_details')}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t('describe_special_needs')}
                          {...field}
                          className={cn(
                            'min-h-20',
                            errors.specialNeedsDescription && 'border-destructive',
                            isRTL && 'text-right'
                          )}
                          dir={isRTL ? 'rtl' : 'ltr'}
                        />
                      </FormControl>
                      <FormDescription>
                        {t('special_needs_details_description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
