'use client'

import React, { useState, useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslation } from 'react-i18next'
import { Upload, File, X, Check, AlertCircle, FileText, Image, FileCheck } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert'

import { documentationSchema, DocumentationFormData } from '@/lib/validation/beneficiary-registration'
import { useMultiStepForm } from '@/components/forms/multi-step-form'
import { cn } from '@/lib/utils'

interface UploadedDocument {
  id: string
  type: 'national_id' | 'income_certificate' | 'family_card' | 'medical_report' | 'other'
  name: string
  file: File
  size: number
  mimeType: string
  uploadProgress?: number
  isUploading?: boolean
  error?: string
}

interface DocumentationUploadStepProps {
  onValidationChange?: (isValid: boolean) => void
}

const documentTypes = [
  { value: 'national_id', labelKey: 'national_id_document', required: true, icon: FileCheck },
  { value: 'income_certificate', labelKey: 'income_certificate', required: false, icon: FileText },
  { value: 'family_card', labelKey: 'family_card', required: false, icon: FileText },
  { value: 'medical_report', labelKey: 'medical_report', required: false, icon: FileText },
  { value: 'other', labelKey: 'other_document', required: false, icon: File },
] as const

const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB
const ACCEPTED_FILE_TYPES = {
  'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
}

export const DocumentationUploadStep: React.FC<DocumentationUploadStepProps> = ({
  onValidationChange,
}) => {
  const { t, i18n } = useTranslation()
  const { formData, updateFormData } = useMultiStepForm()
  const [uploadedDocuments, setUploadedDocuments] = useState<UploadedDocument[]>(
    formData.documentation?.documents || []
  )

  const form = useForm({
    resolver: zodResolver(documentationSchema),
    defaultValues: {
      documents: formData.documentation?.documents || [],
      nationalIdUploaded: formData.documentation?.nationalIdUploaded || false,
      additionalNotes: formData.documentation?.additionalNotes || '',
    },
    mode: 'onChange',
  })

  const { watch, formState: { isValid, errors } } = form

  // Watch for form changes and update parent form data
  React.useEffect(() => {
    const subscription = watch((value) => {
      updateFormData({
        documentation: value as DocumentationFormData,
      })
      onValidationChange?.(isValid)
    })
    return () => subscription.unsubscribe()
  }, [watch, updateFormData, onValidationChange, isValid])

  // Update form when documents change
  React.useEffect(() => {
    const hasNationalId = uploadedDocuments.some(doc => doc.type === 'national_id')
    form.setValue('documents', uploadedDocuments)
    form.setValue('nationalIdUploaded', hasNationalId)
    onValidationChange?.(isValid && uploadedDocuments.length > 0 && hasNationalId)
  }, [uploadedDocuments, form, isValid, onValidationChange])

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return

    Array.from(files).forEach((file) => {
      // Validate file size
      if (file.size > MAX_FILE_SIZE) {
        console.error('File too large:', file.name)
        return
      }

      // Validate file type
      const isValidType = Object.keys(ACCEPTED_FILE_TYPES).some(type =>
        file.type.match(type) ||
        Object.values(ACCEPTED_FILE_TYPES).flat().some(ext =>
          file.name.toLowerCase().endsWith(ext)
        )
      )

      if (!isValidType) {
        console.error('Invalid file type:', file.name)
        return
      }

      const newDocument: UploadedDocument = {
        id: `doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: 'other', // Default type, user can change
        name: file.name,
        file,
        size: file.size,
        mimeType: file.type,
        uploadProgress: 0,
        isUploading: true,
      }

      setUploadedDocuments(prev => [...prev, newDocument])

      // Simulate upload progress
      simulateUpload(newDocument.id)
    })
  }, [])

  const simulateUpload = (documentId: string) => {
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 30
      if (progress >= 100) {
        progress = 100
        clearInterval(interval)
        setUploadedDocuments(prev =>
          prev.map(doc =>
            doc.id === documentId
              ? { ...doc, uploadProgress: 100, isUploading: false }
              : doc
          )
        )
      } else {
        setUploadedDocuments(prev =>
          prev.map(doc =>
            doc.id === documentId
              ? { ...doc, uploadProgress: progress }
              : doc
          )
        )
      }
    }, 200)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const files = e.dataTransfer.files
    handleFileSelect(files)
  }

  const removeDocument = (documentId: string) => {
    setUploadedDocuments(prev => prev.filter(doc => doc.id !== documentId))
  }

  const updateDocumentType = (documentId: string, type: UploadedDocument['type']) => {
    setUploadedDocuments(prev =>
      prev.map(doc =>
        doc.id === documentId ? { ...doc, type } : doc
      )
    )
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return Image
    if (mimeType === 'application/pdf') return FileText
    return File
  }

  const isRTL = i18n.language === 'ar'
  const hasNationalId = uploadedDocuments.some(doc => doc.type === 'national_id')

  return (
    <div className="space-y-6">
      {/* Upload Requirements */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>{t('document_requirements')}</AlertTitle>
        <AlertDescription>
          {t('document_requirements_description')}
        </AlertDescription>
      </Alert>

      {/* Required Documents Notice */}
      {!hasNationalId && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>{t('required_document_missing')}</AlertTitle>
          <AlertDescription>
            {t('national_id_document_required')}
          </AlertDescription>
        </Alert>
      )}

      {/* File Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            {t('upload_documents')}
          </CardTitle>
          <CardDescription>
            {t('upload_documents_description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            className={cn(
              'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
              'border-muted-foreground/25 hover:border-primary/50',
              'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
            )}
            onClick={() => document.getElementById('file-upload')?.click()}
          >
            <input
              id="file-upload"
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
              onChange={(e) => handleFileSelect(e.target.files)}
              className="hidden"
            />
            <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <div className="space-y-2">
              <p className="text-lg font-medium">
                {t('drag_drop_files')}
              </p>
              <p className="text-sm text-muted-foreground">
                {t('or_click_to_browse')}
              </p>
              <p className="text-xs text-muted-foreground">
                {t('supported_formats')}: PDF, DOC, DOCX, JPG, PNG (Max 5MB)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Documents */}
      {uploadedDocuments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileCheck className="h-5 w-5" />
              {t('uploaded_documents')} ({uploadedDocuments.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {uploadedDocuments.map((document) => {
                const FileIcon = getFileIcon(document.mimeType)
                const isRequired = document.type === 'national_id'

                return (
                  <div
                    key={document.id}
                    className="flex items-center gap-4 p-4 border rounded-lg"
                  >
                    <FileIcon className="h-8 w-8 text-muted-foreground flex-shrink-0" />
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium truncate">{document.name}</p>
                        {isRequired && (
                          <Badge variant="destructive" className="text-xs">
                            {t('required')}
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm text-muted-foreground">
                        {formatFileSize(document.size)}
                      </p>

                      {document.isUploading && (
                        <div className="mt-2">
                          <Progress value={document.uploadProgress} className="h-2" />
                          <p className="text-xs text-muted-foreground mt-1">
                            {t('uploading')} {Math.round(document.uploadProgress || 0)}%
                          </p>
                        </div>
                      )}

                      {!document.isUploading && (
                        <div className="mt-2">
                          <select
                            value={document.type}
                            onChange={(e) => updateDocumentType(document.id, e.target.value as UploadedDocument['type'])}
                            className="text-sm border rounded px-2 py-1"
                          >
                            {documentTypes.map((type) => (
                              <option key={type.value} value={type.value}>
                                {t(type.labelKey)}
                              </option>
                            ))}
                          </select>
                        </div>
                      )}
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeDocument(document.id)}
                      className="flex-shrink-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Additional Notes */}
      <Card>
        <CardHeader>
          <CardTitle>{t('additional_notes')}</CardTitle>
          <CardDescription>
            {t('additional_notes_description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <FormField
              control={form.control}
              name="additionalNotes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('notes')} ({t('optional')})</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t('enter_additional_notes')}
                      {...field}
                      className={cn(
                        'min-h-20',
                        errors.additionalNotes && 'border-destructive',
                        isRTL && 'text-right'
                      )}
                      dir={isRTL ? 'rtl' : 'ltr'}
                    />
                  </FormControl>
                  <FormDescription>
                    {t('additional_notes_help')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
