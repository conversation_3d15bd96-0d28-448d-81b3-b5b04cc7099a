"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/beneficiaries/new/page",{

/***/ "(app-pages-browser)/./app/beneficiaries/new/page.tsx":
/*!****************************************!*\
  !*** ./app/beneficiaries/new/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewBeneficiaryPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(app-pages-browser)/./components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/forms/multi-step-form */ \"(app-pages-browser)/./components/forms/multi-step-form.tsx\");\n/* harmony import */ var _components_forms_steps_personal_details_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/forms/steps/personal-details-step */ \"(app-pages-browser)/./components/forms/steps/personal-details-step.tsx\");\n/* harmony import */ var _components_forms_steps_contact_information_step__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/forms/steps/contact-information-step */ \"(app-pages-browser)/./components/forms/steps/contact-information-step.tsx\");\n/* harmony import */ var _components_forms_steps_eligibility_criteria_step__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/forms/steps/eligibility-criteria-step */ \"(app-pages-browser)/./components/forms/steps/eligibility-criteria-step.tsx\");\n/* harmony import */ var _components_forms_steps_documentation_upload_step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/forms/steps/documentation-upload-step */ \"(app-pages-browser)/./components/forms/steps/documentation-upload-step.tsx\");\n/* harmony import */ var _components_forms_steps_review_submit_step__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/forms/steps/review-submit-step */ \"(app-pages-browser)/./components/forms/steps/review-submit-step.tsx\");\n/* harmony import */ var _lib_validation_beneficiary_registration__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/validation/beneficiary-registration */ \"(app-pages-browser)/./lib/validation/beneficiary-registration.ts\");\n/* harmony import */ var _lib_utils_form_validation__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils/form-validation */ \"(app-pages-browser)/./lib/utils/form-validation.ts\");\n/* harmony import */ var _lib_utils_accessibility__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/utils/accessibility */ \"(app-pages-browser)/./lib/utils/accessibility.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewBeneficiaryPage() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)() || {};\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_validation_beneficiary_registration__WEBPACK_IMPORTED_MODULE_14__.defaultFormData);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stepValidation, setStepValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Load draft on component mount - moved here to avoid hooks rule violation\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const savedDraft = localStorage.getItem(\"beneficiary-draft\");\n        if (savedDraft) {\n            try {\n                const draftData = JSON.parse(savedDraft);\n                setFormData(draftData);\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.info(t(\"draft_loaded\"));\n            } catch (error) {\n                console.error(\"Error loading draft:\", error);\n                localStorage.removeItem(\"beneficiary-draft\");\n            }\n        }\n    }, [\n        t\n    ]);\n    if (!session?.user) {\n        return null;\n    }\n    // Check if user has access to beneficiary management\n    const hasAccess = [\n        \"reception_staff\",\n        \"researcher\",\n        \"department_head\",\n        \"admin_manager\",\n        \"minister\",\n        \"system_admin\"\n    ].includes(session.user.role);\n    if (!hasAccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__.DashboardLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: t(\"access_denied\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: t(\"no_registration_access\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    // Define the form steps\n    const steps = [\n        {\n            id: \"personal-details\",\n            title: t(\"personal_details_step\"),\n            description: t(\"personal_details_description\"),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_personal_details_step__WEBPACK_IMPORTED_MODULE_9__.PersonalDetailsStep, {\n                onValidationChange: (isValid)=>setStepValidation((prev)=>({\n                            ...prev,\n                            0: isValid\n                        }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this),\n            isValid: stepValidation[0]\n        },\n        {\n            id: \"contact-information\",\n            title: t(\"contact_information_step\"),\n            description: t(\"contact_information_description\"),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_contact_information_step__WEBPACK_IMPORTED_MODULE_10__.ContactInformationStep, {\n                onValidationChange: (isValid)=>setStepValidation((prev)=>({\n                            ...prev,\n                            1: isValid\n                        }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this),\n            isValid: stepValidation[1]\n        },\n        {\n            id: \"eligibility-criteria\",\n            title: t(\"eligibility_criteria_step\"),\n            description: t(\"select_applicable_categories\"),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_eligibility_criteria_step__WEBPACK_IMPORTED_MODULE_11__.EligibilityCriteriaStep, {\n                onValidationChange: (isValid)=>setStepValidation((prev)=>({\n                            ...prev,\n                            2: isValid\n                        }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this),\n            isValid: stepValidation[2]\n        },\n        {\n            id: \"documentation-upload\",\n            title: t(\"documentation_upload_step\"),\n            description: t(\"upload_documents_description\"),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_documentation_upload_step__WEBPACK_IMPORTED_MODULE_12__.DocumentationUploadStep, {\n                onValidationChange: (isValid)=>setStepValidation((prev)=>({\n                            ...prev,\n                            3: isValid\n                        }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this),\n            isValid: stepValidation[3]\n        },\n        {\n            id: \"review-submit\",\n            title: t(\"review_submit_step\"),\n            description: t(\"review_information_description\"),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_steps_review_submit_step__WEBPACK_IMPORTED_MODULE_13__.ReviewSubmitStep, {\n                onValidationChange: (isValid)=>setStepValidation((prev)=>({\n                            ...prev,\n                            4: isValid\n                        }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this),\n            isValid: stepValidation[4]\n        }\n    ];\n    const handleStepChange = (stepIndex)=>{\n        // Announce step change to screen readers\n        _lib_utils_accessibility__WEBPACK_IMPORTED_MODULE_16__.ScreenReader.announceStepChange(stepIndex, steps.length, steps[stepIndex].title, t);\n    };\n    const handleDataChange = (data)=>{\n        setFormData(data);\n    };\n    const handleSaveDraft = async ()=>{\n        try {\n            // In a real app, this would save to backend\n            localStorage.setItem(\"beneficiary-draft\", JSON.stringify(formData));\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"draft_saved_successfully\"));\n        } catch (error) {\n            console.error(\"Error saving draft:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(t(\"error_saving_draft\"));\n        }\n    };\n    const handleSubmit = async ()=>{\n        setIsSubmitting(true);\n        try {\n            // Check for duplicates before submitting\n            if (formData.personalDetails) {\n                const duplicateCheck = await (0,_lib_utils_form_validation__WEBPACK_IMPORTED_MODULE_15__.checkForDuplicates)({\n                    nationalId: formData.personalDetails.nationalId,\n                    fullNameAr: formData.personalDetails.fullNameAr,\n                    fullNameEn: formData.personalDetails.fullNameEn,\n                    phoneNumber: formData.contactInformation?.phoneNumber,\n                    email: formData.contactInformation?.email\n                });\n                if (duplicateCheck.isDuplicate) {\n                    sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(t(\"duplicate_beneficiary_found\"), {\n                        description: t(\"duplicate_check_failed\")\n                    });\n                    setIsSubmitting(false);\n                    return;\n                }\n            }\n            // Simulate API submission\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // In a real app, this would submit to backend\n            console.log(\"Submitting beneficiary registration:\", formData);\n            // Clear draft\n            localStorage.removeItem(\"beneficiary-draft\");\n            // Show success message\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success(t(\"beneficiary_registered_successfully\"), {\n                description: t(\"beneficiary_registration_success_description\")\n            });\n            // Announce success to screen readers\n            _lib_utils_accessibility__WEBPACK_IMPORTED_MODULE_16__.ScreenReader.announceProgress(100, t);\n            // Redirect to beneficiaries list\n            router.push(\"/beneficiaries\");\n        } catch (error) {\n            console.error(\"Error submitting form:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(t(\"error_submitting_form\"));\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleGoBack = ()=>{\n        router.push(\"/beneficiaries\");\n    };\n    // Load draft on component mount\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const savedDraft = localStorage.getItem(\"beneficiary-draft\");\n        if (savedDraft) {\n            try {\n                const draftData = JSON.parse(savedDraft);\n                setFormData(draftData);\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.info(t(\"draft_loaded\"));\n            } catch (error) {\n                console.error(\"Error loading draft:\", error);\n                localStorage.removeItem(\"beneficiary-draft\");\n            }\n        }\n    }, [\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_6__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"outline\",\n                            onClick: handleGoBack,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                t(\"back_to_beneficiaries\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: t(\"register_new_beneficiary\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: t(\"beneficiary_registration_description\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_multi_step_form__WEBPACK_IMPORTED_MODULE_8__.MultiStepForm, {\n                    steps: steps,\n                    initialData: formData,\n                    onStepChange: handleStepChange,\n                    onDataChange: handleDataChange,\n                    onSaveDraft: handleSaveDraft,\n                    onSubmit: handleSubmit,\n                    showSaveDraft: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this),\n                isSubmitting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-card p-6 rounded-lg shadow-lg text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: t(\"submitting_registration\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: t(\"please_wait\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\beneficiaries\\\\new\\\\page.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_s(NewBeneficiaryPage, \"8VxgcIORJuod1eouOnVhezdX0u0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = NewBeneficiaryPage;\nvar _c;\n$RefreshReg$(_c, \"NewBeneficiaryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/beneficiaries/new/page.tsx\n"));

/***/ })

});