"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Te),\n/* harmony export */   toast: () => (/* binding */ Jt),\n/* harmony export */   useSonner: () => (/* binding */ we)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ \n\n\nvar Ct = (s)=>{\n    switch(s){\n        case \"success\":\n            return $t;\n        case \"info\":\n            return _t;\n        case \"warning\":\n            return Wt;\n        case \"error\":\n            return Ut;\n        default:\n            return null;\n    }\n}, Ft = Array(12).fill(0), It = ({ visible: s })=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-loading-wrapper\",\n        \"data-visible\": s\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, Ft.map((o, t)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${t}`\n        })))), $t = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n})), Wt = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n})), _t = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n})), Ut = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\n\nvar Dt = ()=>{\n    let [s, o] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let t = ()=>{\n            o(document.hidden);\n        };\n        return document.addEventListener(\"visibilitychange\", t), ()=>window.removeEventListener(\"visibilitychange\", t);\n    }, []), s;\n};\nvar ct = 1, ut = class {\n    constructor(){\n        this.subscribe = (o)=>(this.subscribers.push(o), ()=>{\n                let t = this.subscribers.indexOf(o);\n                this.subscribers.splice(t, 1);\n            });\n        this.publish = (o)=>{\n            this.subscribers.forEach((t)=>t(o));\n        };\n        this.addToast = (o)=>{\n            this.publish(o), this.toasts = [\n                ...this.toasts,\n                o\n            ];\n        };\n        this.create = (o)=>{\n            var b;\n            let { message: t, ...n } = o, h = typeof (o == null ? void 0 : o.id) == \"number\" || ((b = o.id) == null ? void 0 : b.length) > 0 ? o.id : ct++, u = this.toasts.find((d)=>d.id === h), g = o.dismissible === void 0 ? !0 : o.dismissible;\n            return u ? this.toasts = this.toasts.map((d)=>d.id === h ? (this.publish({\n                    ...d,\n                    ...o,\n                    id: h,\n                    title: t\n                }), {\n                    ...d,\n                    ...o,\n                    id: h,\n                    dismissible: g,\n                    title: t\n                }) : d) : this.addToast({\n                title: t,\n                ...n,\n                dismissible: g,\n                id: h\n            }), h;\n        };\n        this.dismiss = (o)=>(o || this.toasts.forEach((t)=>{\n                this.subscribers.forEach((n)=>n({\n                        id: t.id,\n                        dismiss: !0\n                    }));\n            }), this.subscribers.forEach((t)=>t({\n                    id: o,\n                    dismiss: !0\n                })), o);\n        this.message = (o, t)=>this.create({\n                ...t,\n                message: o\n            });\n        this.error = (o, t)=>this.create({\n                ...t,\n                message: o,\n                type: \"error\"\n            });\n        this.success = (o, t)=>this.create({\n                ...t,\n                type: \"success\",\n                message: o\n            });\n        this.info = (o, t)=>this.create({\n                ...t,\n                type: \"info\",\n                message: o\n            });\n        this.warning = (o, t)=>this.create({\n                ...t,\n                type: \"warning\",\n                message: o\n            });\n        this.loading = (o, t)=>this.create({\n                ...t,\n                type: \"loading\",\n                message: o\n            });\n        this.promise = (o, t)=>{\n            if (!t) return;\n            let n;\n            t.loading !== void 0 && (n = this.create({\n                ...t,\n                promise: o,\n                type: \"loading\",\n                message: t.loading,\n                description: typeof t.description != \"function\" ? t.description : void 0\n            }));\n            let h = o instanceof Promise ? o : o(), u = n !== void 0;\n            return h.then(async (g)=>{\n                if (Ot(g) && !g.ok) {\n                    u = !1;\n                    let b = typeof t.error == \"function\" ? await t.error(`HTTP error! status: ${g.status}`) : t.error, d = typeof t.description == \"function\" ? await t.description(`HTTP error! status: ${g.status}`) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"error\",\n                        message: b,\n                        description: d\n                    });\n                } else if (t.success !== void 0) {\n                    u = !1;\n                    let b = typeof t.success == \"function\" ? await t.success(g) : t.success, d = typeof t.description == \"function\" ? await t.description(g) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"success\",\n                        message: b,\n                        description: d\n                    });\n                }\n            }).catch(async (g)=>{\n                if (t.error !== void 0) {\n                    u = !1;\n                    let b = typeof t.error == \"function\" ? await t.error(g) : t.error, d = typeof t.description == \"function\" ? await t.description(g) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"error\",\n                        message: b,\n                        description: d\n                    });\n                }\n            }).finally(()=>{\n                var g;\n                u && (this.dismiss(n), n = void 0), (g = t.finally) == null || g.call(t);\n            }), n;\n        };\n        this.custom = (o, t)=>{\n            let n = (t == null ? void 0 : t.id) || ct++;\n            return this.create({\n                jsx: o(n),\n                id: n,\n                ...t\n            }), n;\n        };\n        this.subscribers = [], this.toasts = [];\n    }\n}, v = new ut, Vt = (s, o)=>{\n    let t = (o == null ? void 0 : o.id) || ct++;\n    return v.addToast({\n        title: s,\n        ...o,\n        id: t\n    }), t;\n}, Ot = (s)=>s && typeof s == \"object\" && \"ok\" in s && typeof s.ok == \"boolean\" && \"status\" in s && typeof s.status == \"number\", Kt = Vt, Xt = ()=>v.toasts, Jt = Object.assign(Kt, {\n    success: v.success,\n    info: v.info,\n    warning: v.warning,\n    error: v.error,\n    custom: v.custom,\n    message: v.message,\n    promise: v.promise,\n    dismiss: v.dismiss,\n    loading: v.loading\n}, {\n    getHistory: Xt\n});\nfunction ft(s, { insertAt: o } = {}) {\n    if (!s || typeof document == \"undefined\") return;\n    let t = document.head || document.getElementsByTagName(\"head\")[0], n = document.createElement(\"style\");\n    n.type = \"text/css\", o === \"top\" && t.firstChild ? t.insertBefore(n, t.firstChild) : t.appendChild(n), n.styleSheet ? n.styleSheet.cssText = s : n.appendChild(document.createTextNode(s));\n}\nft(`:where(html[dir=\"ltr\"]),:where([data-sonner-toaster][dir=\"ltr\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\"rtl\"]),:where([data-sonner-toaster][dir=\"rtl\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position=\"right\"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position=\"left\"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position=\"center\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\"top\"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position=\"bottom\"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\"true\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\"top\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\"bottom\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\"true\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\"dark\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\"true\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\"true\"]):before{content:\"\";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\"top\"][data-swiping=\"true\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\"bottom\"][data-swiping=\"true\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\"false\"][data-removed=\"true\"]):before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\"true\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"][data-styled=\"true\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\"false\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\"true\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"true\"][data-swipe-out=\"false\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"false\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n`);\nfunction U(s) {\n    return s.label !== void 0;\n}\nvar qt = 3, Qt = \"32px\", Zt = 4e3, te = 356, ee = 14, oe = 20, ae = 200;\nfunction ne(...s) {\n    return s.filter(Boolean).join(\" \");\n}\nvar se = (s)=>{\n    var yt, xt, vt, wt, Tt, St, Rt, Et, Nt, Pt;\n    let { invert: o, toast: t, unstyled: n, interacting: h, setHeights: u, visibleToasts: g, heights: b, index: d, toasts: q, expanded: $, removeToast: V, defaultRichColors: Q, closeButton: i, style: O, cancelButtonStyle: K, actionButtonStyle: Z, className: tt = \"\", descriptionClassName: et = \"\", duration: X, position: ot, gap: w, loadingIcon: j, expandByDefault: W, classNames: r, icons: I, closeButtonAriaLabel: at = \"Close toast\", pauseWhenPageIsHidden: k, cn: T } = s, [z, nt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [D, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [st, N] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [M, rt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [c, m] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), [y, S] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), A = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), l = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), _ = d === 0, J = d + 1 <= g, x = t.type, P = t.dismissible !== !1, Mt = t.className || \"\", At = t.descriptionClassName || \"\", G = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>b.findIndex((a)=>a.toastId === t.id) || 0, [\n        b,\n        t.id\n    ]), Lt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        var a;\n        return (a = t.closeButton) != null ? a : i;\n    }, [\n        t.closeButton,\n        i\n    ]), mt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>t.duration || X || Zt, [\n        t.duration,\n        X\n    ]), it = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), Y = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), pt = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), F = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), [gt, zt] = ot.split(\"-\"), ht = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>b.reduce((a, f, p)=>p >= G ? a : a + f.height, 0), [\n        b,\n        G\n    ]), bt = Dt(), jt = t.invert || o, lt = x === \"loading\";\n    Y.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>G * w + ht, [\n        G,\n        ht\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        nt(!0);\n    }, []), react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(()=>{\n        if (!z) return;\n        let a = l.current, f = a.style.height;\n        a.style.height = \"auto\";\n        let p = a.getBoundingClientRect().height;\n        a.style.height = f, S(p), u((B)=>B.find((R)=>R.toastId === t.id) ? B.map((R)=>R.toastId === t.id ? {\n                    ...R,\n                    height: p\n                } : R) : [\n                {\n                    toastId: t.id,\n                    height: p,\n                    position: t.position\n                },\n                ...B\n            ]);\n    }, [\n        z,\n        t.title,\n        t.description,\n        u,\n        t.id\n    ]);\n    let L = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        H(!0), m(Y.current), u((a)=>a.filter((f)=>f.toastId !== t.id)), setTimeout(()=>{\n            V(t);\n        }, ae);\n    }, [\n        t,\n        V,\n        u,\n        Y\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (t.promise && x === \"loading\" || t.duration === 1 / 0 || t.type === \"loading\") return;\n        let a, f = mt;\n        return $ || h || k && bt ? (()=>{\n            if (pt.current < it.current) {\n                let C = new Date().getTime() - it.current;\n                f = f - C;\n            }\n            pt.current = new Date().getTime();\n        })() : (()=>{\n            f !== 1 / 0 && (it.current = new Date().getTime(), a = setTimeout(()=>{\n                var C;\n                (C = t.onAutoClose) == null || C.call(t, t), L();\n            }, f));\n        })(), ()=>clearTimeout(a);\n    }, [\n        $,\n        h,\n        W,\n        t,\n        mt,\n        L,\n        t.promise,\n        x,\n        k,\n        bt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let a = l.current;\n        if (a) {\n            let f = a.getBoundingClientRect().height;\n            return S(f), u((p)=>[\n                    {\n                        toastId: t.id,\n                        height: f,\n                        position: t.position\n                    },\n                    ...p\n                ]), ()=>u((p)=>p.filter((B)=>B.toastId !== t.id));\n        }\n    }, [\n        u,\n        t.id\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        t.delete && L();\n    }, [\n        L,\n        t.delete\n    ]);\n    function Yt() {\n        return I != null && I.loading ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loader\",\n            \"data-visible\": x === \"loading\"\n        }, I.loading) : j ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loader\",\n            \"data-visible\": x === \"loading\"\n        }, j) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(It, {\n            visible: x === \"loading\"\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        \"aria-live\": t.important ? \"assertive\" : \"polite\",\n        \"aria-atomic\": \"true\",\n        role: \"status\",\n        tabIndex: 0,\n        ref: l,\n        className: T(tt, Mt, r == null ? void 0 : r.toast, (yt = t == null ? void 0 : t.classNames) == null ? void 0 : yt.toast, r == null ? void 0 : r.default, r == null ? void 0 : r[x], (xt = t == null ? void 0 : t.classNames) == null ? void 0 : xt[x]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (vt = t.richColors) != null ? vt : Q,\n        \"data-styled\": !(t.jsx || t.unstyled || n),\n        \"data-mounted\": z,\n        \"data-promise\": !!t.promise,\n        \"data-removed\": D,\n        \"data-visible\": J,\n        \"data-y-position\": gt,\n        \"data-x-position\": zt,\n        \"data-index\": d,\n        \"data-front\": _,\n        \"data-swiping\": st,\n        \"data-dismissible\": P,\n        \"data-type\": x,\n        \"data-invert\": jt,\n        \"data-swipe-out\": M,\n        \"data-expanded\": !!($ || W && z),\n        style: {\n            \"--index\": d,\n            \"--toasts-before\": d,\n            \"--z-index\": q.length - d,\n            \"--offset\": `${D ? c : Y.current}px`,\n            \"--initial-height\": W ? \"auto\" : `${y}px`,\n            ...O,\n            ...t.style\n        },\n        onPointerDown: (a)=>{\n            lt || !P || (A.current = new Date, m(Y.current), a.target.setPointerCapture(a.pointerId), a.target.tagName !== \"BUTTON\" && (N(!0), F.current = {\n                x: a.clientX,\n                y: a.clientY\n            }));\n        },\n        onPointerUp: ()=>{\n            var B, C, R, dt;\n            if (M || !P) return;\n            F.current = null;\n            let a = Number(((B = l.current) == null ? void 0 : B.style.getPropertyValue(\"--swipe-amount\").replace(\"px\", \"\")) || 0), f = new Date().getTime() - ((C = A.current) == null ? void 0 : C.getTime()), p = Math.abs(a) / f;\n            if (Math.abs(a) >= oe || p > .11) {\n                m(Y.current), (R = t.onDismiss) == null || R.call(t, t), L(), rt(!0);\n                return;\n            }\n            (dt = l.current) == null || dt.style.setProperty(\"--swipe-amount\", \"0px\"), N(!1);\n        },\n        onPointerMove: (a)=>{\n            var Bt;\n            if (!F.current || !P) return;\n            let f = a.clientY - F.current.y, p = a.clientX - F.current.x, C = (gt === \"top\" ? Math.min : Math.max)(0, f), R = a.pointerType === \"touch\" ? 10 : 2;\n            Math.abs(C) > R ? (Bt = l.current) == null || Bt.style.setProperty(\"--swipe-amount\", `${f}px`) : Math.abs(p) > R && (F.current = null);\n        }\n    }, Lt && !t.jsx ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": at,\n        \"data-disabled\": lt,\n        \"data-close-button\": !0,\n        onClick: lt || !P ? ()=>{} : ()=>{\n            var a;\n            L(), (a = t.onDismiss) == null || a.call(t, t);\n        },\n        className: T(r == null ? void 0 : r.closeButton, (wt = t == null ? void 0 : t.classNames) == null ? void 0 : wt.closeButton)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"12\",\n        height: \"12\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"1.5\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        x1: \"18\",\n        y1: \"6\",\n        x2: \"6\",\n        y2: \"18\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        x1: \"6\",\n        y1: \"6\",\n        x2: \"18\",\n        y2: \"18\"\n    }))) : null, t.jsx || /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.title) ? t.jsx || t.title : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, x || t.icon || t.promise ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: T(r == null ? void 0 : r.icon, (Tt = t == null ? void 0 : t.classNames) == null ? void 0 : Tt.icon)\n    }, t.promise || t.type === \"loading\" && !t.icon ? t.icon || Yt() : null, t.type !== \"loading\" ? t.icon || (I == null ? void 0 : I[x]) || Ct(x) : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: T(r == null ? void 0 : r.content, (St = t == null ? void 0 : t.classNames) == null ? void 0 : St.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: T(r == null ? void 0 : r.title, (Rt = t == null ? void 0 : t.classNames) == null ? void 0 : Rt.title)\n    }, t.title), t.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: T(et, At, r == null ? void 0 : r.description, (Et = t == null ? void 0 : t.classNames) == null ? void 0 : Et.description)\n    }, t.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.cancel) ? t.cancel : t.cancel && U(t.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-cancel\": !0,\n        style: t.cancelButtonStyle || K,\n        onClick: (a)=>{\n            var f, p;\n            U(t.cancel) && P && ((p = (f = t.cancel).onClick) == null || p.call(f, a), L());\n        },\n        className: T(r == null ? void 0 : r.cancelButton, (Nt = t == null ? void 0 : t.classNames) == null ? void 0 : Nt.cancelButton)\n    }, t.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.action) ? t.action : t.action && U(t.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-action\": !0,\n        style: t.actionButtonStyle || Z,\n        onClick: (a)=>{\n            var f, p;\n            U(t.action) && (a.defaultPrevented || ((p = (f = t.action).onClick) == null || p.call(f, a), L()));\n        },\n        className: T(r == null ? void 0 : r.actionButton, (Pt = t == null ? void 0 : t.classNames) == null ? void 0 : Pt.actionButton)\n    }, t.action.label) : null));\n};\nfunction Ht() {\n    if (true) return \"ltr\";\n    let s = document.documentElement.getAttribute(\"dir\");\n    return s === \"auto\" || !s ? window.getComputedStyle(document.documentElement).direction : s;\n}\nfunction we() {\n    let [s, o] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>v.subscribe((t)=>{\n            o((n)=>{\n                if (\"dismiss\" in t && t.dismiss) return n.filter((u)=>u.id !== t.id);\n                let h = n.findIndex((u)=>u.id === t.id);\n                if (h !== -1) {\n                    let u = [\n                        ...n\n                    ];\n                    return u[h] = {\n                        ...u[h],\n                        ...t\n                    }, u;\n                } else return [\n                    t,\n                    ...n\n                ];\n            });\n        }), []), {\n        toasts: s\n    };\n}\nvar Te = (s)=>{\n    let { invert: o, position: t = \"bottom-right\", hotkey: n = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand: h, closeButton: u, className: g, offset: b, theme: d = \"light\", richColors: q, duration: $, style: V, visibleToasts: Q = qt, toastOptions: i, dir: O = Ht(), gap: K = ee, loadingIcon: Z, icons: tt, containerAriaLabel: et = \"Notifications\", pauseWhenPageIsHidden: X, cn: ot = ne } = s, [w, j] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), W = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Array.from(new Set([\n            t\n        ].concat(w.filter((c)=>c.position).map((c)=>c.position)))), [\n        w,\n        t\n    ]), [r, I] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), [at, k] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [T, z] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [nt, D] = react__WEBPACK_IMPORTED_MODULE_0__.useState(d !== \"system\" ? d :  false ? 0 : \"light\"), H = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), st = n.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\"), N = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), M = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!1), rt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((c)=>{\n        var m;\n        (m = w.find((y)=>y.id === c.id)) != null && m.delete || v.dismiss(c.id), j((y)=>y.filter(({ id: S })=>S !== c.id));\n    }, [\n        w\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>v.subscribe((c)=>{\n            if (c.dismiss) {\n                j((m)=>m.map((y)=>y.id === c.id ? {\n                            ...y,\n                            delete: !0\n                        } : y));\n                return;\n            }\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    j((m)=>{\n                        let y = m.findIndex((S)=>S.id === c.id);\n                        return y !== -1 ? [\n                            ...m.slice(0, y),\n                            {\n                                ...m[y],\n                                ...c\n                            },\n                            ...m.slice(y + 1)\n                        ] : [\n                            c,\n                            ...m\n                        ];\n                    });\n                });\n            });\n        }), []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (d !== \"system\") {\n            D(d);\n            return;\n        }\n        d === \"system\" && (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? D(\"dark\") : D(\"light\")),  false && 0;\n    }, [\n        d\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        w.length <= 1 && k(!1);\n    }, [\n        w\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let c = (m)=>{\n            var S, A;\n            n.every((l)=>m[l] || m.code === l) && (k(!0), (S = H.current) == null || S.focus()), m.code === \"Escape\" && (document.activeElement === H.current || (A = H.current) != null && A.contains(document.activeElement)) && k(!1);\n        };\n        return document.addEventListener(\"keydown\", c), ()=>document.removeEventListener(\"keydown\", c);\n    }, [\n        n\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (H.current) return ()=>{\n            N.current && (N.current.focus({\n                preventScroll: !0\n            }), N.current = null, M.current = !1);\n        };\n    }, [\n        H.current\n    ]), w.length ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        \"aria-label\": `${et} ${st}`,\n        tabIndex: -1\n    }, W.map((c, m)=>{\n        var A;\n        let [y, S] = c.split(\"-\");\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: c,\n            dir: O === \"auto\" ? Ht() : O,\n            tabIndex: -1,\n            ref: H,\n            className: g,\n            \"data-sonner-toaster\": !0,\n            \"data-theme\": nt,\n            \"data-y-position\": y,\n            \"data-x-position\": S,\n            style: {\n                \"--front-toast-height\": `${((A = r[0]) == null ? void 0 : A.height) || 0}px`,\n                \"--offset\": typeof b == \"number\" ? `${b}px` : b || Qt,\n                \"--width\": `${te}px`,\n                \"--gap\": `${K}px`,\n                ...V\n            },\n            onBlur: (l)=>{\n                M.current && !l.currentTarget.contains(l.relatedTarget) && (M.current = !1, N.current && (N.current.focus({\n                    preventScroll: !0\n                }), N.current = null));\n            },\n            onFocus: (l)=>{\n                l.target instanceof HTMLElement && l.target.dataset.dismissible === \"false\" || M.current || (M.current = !0, N.current = l.relatedTarget);\n            },\n            onMouseEnter: ()=>k(!0),\n            onMouseMove: ()=>k(!0),\n            onMouseLeave: ()=>{\n                T || k(!1);\n            },\n            onPointerDown: (l)=>{\n                l.target instanceof HTMLElement && l.target.dataset.dismissible === \"false\" || z(!0);\n            },\n            onPointerUp: ()=>z(!1)\n        }, w.filter((l)=>!l.position && m === 0 || l.position === c).map((l, _)=>{\n            var J, x;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(se, {\n                key: l.id,\n                icons: tt,\n                index: _,\n                toast: l,\n                defaultRichColors: q,\n                duration: (J = i == null ? void 0 : i.duration) != null ? J : $,\n                className: i == null ? void 0 : i.className,\n                descriptionClassName: i == null ? void 0 : i.descriptionClassName,\n                invert: o,\n                visibleToasts: Q,\n                closeButton: (x = i == null ? void 0 : i.closeButton) != null ? x : u,\n                interacting: T,\n                position: c,\n                style: i == null ? void 0 : i.style,\n                unstyled: i == null ? void 0 : i.unstyled,\n                classNames: i == null ? void 0 : i.classNames,\n                cancelButtonStyle: i == null ? void 0 : i.cancelButtonStyle,\n                actionButtonStyle: i == null ? void 0 : i.actionButtonStyle,\n                removeToast: rt,\n                toasts: w.filter((P)=>P.position == l.position),\n                heights: r.filter((P)=>P.position == l.position),\n                setHeights: I,\n                expandByDefault: h,\n                gap: K,\n                loadingIcon: Z,\n                expanded: at,\n                pauseWhenPageIsHidden: X,\n                cn: ot\n            });\n        }));\n    })) : null;\n};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;