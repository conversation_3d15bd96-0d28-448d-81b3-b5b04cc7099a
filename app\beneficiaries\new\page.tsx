'use client'

import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { ArrowLeft } from 'lucide-react'

import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Button } from '@/components/ui/button'
import { MultiStepForm, Step } from '@/components/forms/multi-step-form'
import { PersonalDetailsStep } from '@/components/forms/steps/personal-details-step'
import { ContactInformationStep } from '@/components/forms/steps/contact-information-step'
import { EligibilityCriteriaStep } from '@/components/forms/steps/eligibility-criteria-step'
import { DocumentationUploadStep } from '@/components/forms/steps/documentation-upload-step'
import { ReviewSubmitStep } from '@/components/forms/steps/review-submit-step'
import { defaultFormData, BeneficiaryRegistrationFormData } from '@/lib/validation/beneficiary-registration'
import { checkForDuplicates } from '@/lib/utils/form-validation'
import { ScreenReader } from '@/lib/utils/accessibility'

export default function NewBeneficiaryPage() {
  const { data: session } = useSession() || {}
  const { t } = useTranslation()
  const router = useRouter()
  const [formData, setFormData] = useState<Partial<BeneficiaryRegistrationFormData>>(defaultFormData)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [stepValidation, setStepValidation] = useState<Record<number, boolean>>({})

  if (!session?.user) {
    return null
  }

  // Check if user has access to beneficiary management
  const hasAccess = ['reception_staff', 'researcher', 'department_head', 'admin_manager', 'minister', 'system_admin'].includes(session.user.role)

  if (!hasAccess) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">{t('access_denied')}</h2>
            <p className="text-muted-foreground">{t('no_registration_access')}</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // Define the form steps
  const steps: Step[] = [
    {
      id: 'personal-details',
      title: t('personal_details_step'),
      description: t('personal_details_description'),
      component: (
        <PersonalDetailsStep
          onValidationChange={(isValid) => setStepValidation(prev => ({ ...prev, 0: isValid }))}
        />
      ),
      isValid: stepValidation[0],
    },
    {
      id: 'contact-information',
      title: t('contact_information_step'),
      description: t('contact_information_description'),
      component: (
        <ContactInformationStep
          onValidationChange={(isValid) => setStepValidation(prev => ({ ...prev, 1: isValid }))}
        />
      ),
      isValid: stepValidation[1],
    },
    {
      id: 'eligibility-criteria',
      title: t('eligibility_criteria_step'),
      description: t('select_applicable_categories'),
      component: (
        <EligibilityCriteriaStep
          onValidationChange={(isValid) => setStepValidation(prev => ({ ...prev, 2: isValid }))}
        />
      ),
      isValid: stepValidation[2],
    },
    {
      id: 'documentation-upload',
      title: t('documentation_upload_step'),
      description: t('upload_documents_description'),
      component: (
        <DocumentationUploadStep
          onValidationChange={(isValid) => setStepValidation(prev => ({ ...prev, 3: isValid }))}
        />
      ),
      isValid: stepValidation[3],
    },
    {
      id: 'review-submit',
      title: t('review_submit_step'),
      description: t('review_information_description'),
      component: (
        <ReviewSubmitStep
          onValidationChange={(isValid) => setStepValidation(prev => ({ ...prev, 4: isValid }))}
        />
      ),
      isValid: stepValidation[4],
    },
  ]

  const handleStepChange = (stepIndex: number) => {
    // Announce step change to screen readers
    ScreenReader.announceStepChange(stepIndex, steps.length, steps[stepIndex].title, t)
  }

  const handleDataChange = (data: Record<string, any>) => {
    setFormData(data)
  }

  const handleSaveDraft = async () => {
    try {
      // In a real app, this would save to backend
      localStorage.setItem('beneficiary-draft', JSON.stringify(formData))
      toast.success(t('draft_saved_successfully'))
    } catch (error) {
      console.error('Error saving draft:', error)
      toast.error(t('error_saving_draft'))
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)

    try {
      // Check for duplicates before submitting
      if (formData.personalDetails) {
        const duplicateCheck = await checkForDuplicates({
          nationalId: formData.personalDetails.nationalId,
          fullNameAr: formData.personalDetails.fullNameAr,
          fullNameEn: formData.personalDetails.fullNameEn,
          phoneNumber: formData.contactInformation?.phoneNumber,
          email: formData.contactInformation?.email,
        })

        if (duplicateCheck.isDuplicate) {
          toast.error(t('duplicate_beneficiary_found'), {
            description: t('duplicate_check_failed'),
          })
          setIsSubmitting(false)
          return
        }
      }

      // Simulate API submission
      await new Promise(resolve => setTimeout(resolve, 2000))

      // In a real app, this would submit to backend
      console.log('Submitting beneficiary registration:', formData)

      // Clear draft
      localStorage.removeItem('beneficiary-draft')

      // Show success message
      toast.success(t('beneficiary_registered_successfully'), {
        description: t('beneficiary_registration_success_description'),
      })

      // Announce success to screen readers
      ScreenReader.announceProgress(100, t)

      // Redirect to beneficiaries list
      router.push('/beneficiaries')

    } catch (error) {
      console.error('Error submitting form:', error)
      toast.error(t('error_submitting_form'))
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleGoBack = () => {
    router.push('/beneficiaries')
  }

  // Load draft on component mount
  React.useEffect(() => {
    const savedDraft = localStorage.getItem('beneficiary-draft')
    if (savedDraft) {
      try {
        const draftData = JSON.parse(savedDraft)
        setFormData(draftData)
        toast.info(t('draft_loaded'))
      } catch (error) {
        console.error('Error loading draft:', error)
        localStorage.removeItem('beneficiary-draft')
      }
    }
  }, [t])

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={handleGoBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('back_to_beneficiaries')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{t('register_new_beneficiary')}</h1>
            <p className="text-muted-foreground">
              {t('beneficiary_registration_description')}
            </p>
          </div>
        </div>

        {/* Multi-Step Form */}
        <MultiStepForm
          steps={steps}
          initialData={formData}
          onStepChange={handleStepChange}
          onDataChange={handleDataChange}
          onSaveDraft={handleSaveDraft}
          onSubmit={handleSubmit}
          showSaveDraft={true}
        />

        {/* Loading overlay */}
        {isSubmitting && (
          <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="bg-card p-6 rounded-lg shadow-lg text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="font-medium">{t('submitting_registration')}</p>
              <p className="text-sm text-muted-foreground">{t('please_wait')}</p>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
