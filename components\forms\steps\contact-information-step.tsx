'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslation } from 'react-i18next'
import { Phone, Mail, MapPin, Building, Map } from 'lucide-react'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'

import { contactInformationSchema, ContactInformationFormData } from '@/lib/validation/beneficiary-registration'
import { useMultiStepForm } from '@/components/forms/multi-step-form'
import { cn } from '@/lib/utils'

// Saudi regions for the dropdown
const saudiRegions = [
  { value: 'riyadh', label: { ar: 'منطقة الرياض', en: 'Riyadh Region' } },
  { value: 'makkah', label: { ar: 'منطقة مكة المكرمة', en: 'Makkah Region' } },
  { value: 'madinah', label: { ar: 'منطقة المدينة المنورة', en: 'Madinah Region' } },
  { value: 'qassim', label: { ar: 'منطقة القصيم', en: 'Qassim Region' } },
  { value: 'eastern', label: { ar: 'المنطقة الشرقية', en: 'Eastern Region' } },
  { value: 'asir', label: { ar: 'منطقة عسير', en: 'Asir Region' } },
  { value: 'tabuk', label: { ar: 'منطقة تبوك', en: 'Tabuk Region' } },
  { value: 'hail', label: { ar: 'منطقة حائل', en: 'Hail Region' } },
  { value: 'northern_borders', label: { ar: 'منطقة الحدود الشمالية', en: 'Northern Borders Region' } },
  { value: 'jazan', label: { ar: 'منطقة جازان', en: 'Jazan Region' } },
  { value: 'najran', label: { ar: 'منطقة نجران', en: 'Najran Region' } },
  { value: 'bahah', label: { ar: 'منطقة الباحة', en: 'Bahah Region' } },
  { value: 'jouf', label: { ar: 'منطقة الجوف', en: 'Jouf Region' } },
]

interface ContactInformationStepProps {
  onValidationChange?: (isValid: boolean) => void
}

export const ContactInformationStep: React.FC<ContactInformationStepProps> = ({
  onValidationChange,
}) => {
  const { t, i18n } = useTranslation()
  const { formData, updateFormData } = useMultiStepForm()

  const form = useForm({
    resolver: zodResolver(contactInformationSchema),
    defaultValues: {
      phoneNumber: formData.contactInformation?.phoneNumber || '',
      email: formData.contactInformation?.email || '',
      address: formData.contactInformation?.address || '',
      city: formData.contactInformation?.city || '',
      region: formData.contactInformation?.region || '',
      postalCode: formData.contactInformation?.postalCode || '',
    },
    mode: 'onChange',
  })

  const { watch, formState: { isValid, errors } } = form

  // Watch for form changes and update parent form data
  React.useEffect(() => {
    const subscription = watch((value) => {
      updateFormData({
        contactInformation: value as ContactInformationFormData,
      })
      onValidationChange?.(isValid)
    })
    return () => subscription.unsubscribe()
  }, [watch, updateFormData, onValidationChange, isValid])

  // Notify parent of initial validation state
  React.useEffect(() => {
    onValidationChange?.(isValid)
  }, [isValid, onValidationChange])

  const isRTL = i18n.language === 'ar'

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            {t('contact_info')}
          </CardTitle>
          <CardDescription>
            {t('contact_information_description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <div className="grid gap-6">
              {/* Phone and Email */}
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        {t('phone_number')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('enter_phone_number')}
                          {...field}
                          className={cn(
                            'font-mono',
                            errors.phoneNumber && 'border-destructive'
                          )}
                          dir="ltr"
                          type="tel"
                        />
                      </FormControl>
                      <FormDescription>
                        {t('phone_number_description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        {t('email')} ({t('optional')})
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('enter_email')}
                          {...field}
                          className={cn(
                            errors.email && 'border-destructive'
                          )}
                          dir="ltr"
                          type="email"
                        />
                      </FormControl>
                      <FormDescription>
                        {t('email_description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Address */}
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      {t('address')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('enter_address')}
                        {...field}
                        className={cn(
                          errors.address && 'border-destructive',
                          isRTL && 'text-right'
                        )}
                        dir={isRTL ? 'rtl' : 'ltr'}
                      />
                    </FormControl>
                    <FormDescription>
                      {t('address_description')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* City and Region */}
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Building className="h-4 w-4" />
                        {t('city')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('enter_city')}
                          {...field}
                          className={cn(
                            errors.city && 'border-destructive',
                            isRTL && 'text-right'
                          )}
                          dir={isRTL ? 'rtl' : 'ltr'}
                        />
                      </FormControl>
                      <FormDescription>
                        {t('city_description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="region"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Map className="h-4 w-4" />
                        {t('region')}
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className={cn(
                            errors.region && 'border-destructive'
                          )}>
                            <SelectValue placeholder={t('select_region')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {saudiRegions.map((region) => (
                            <SelectItem key={region.value} value={region.value}>
                              {isRTL ? region.label.ar : region.label.en}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {t('region_description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Postal Code */}
              <FormField
                control={form.control}
                name="postalCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      {t('postal_code')} ({t('optional')})
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('enter_postal_code')}
                        {...field}
                        className={cn(
                          'font-mono',
                          errors.postalCode && 'border-destructive'
                        )}
                        dir="ltr"
                        maxLength={5}
                      />
                    </FormControl>
                    <FormDescription>
                      {t('postal_code_description')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
